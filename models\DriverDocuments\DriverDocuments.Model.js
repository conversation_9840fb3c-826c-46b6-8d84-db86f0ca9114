// models/Form.js
import mongoose from "mongoose";

// Define the schema
const DriverDocumentsSchema = new mongoose.Schema({
  document: {
    type: String,
    trim: true,
    required: true,
  },
  documentExpire:{
    type:String,
    required:true
  },
  documentname: {
    type: String,
    trim: true,
  },
  Driverid:{
    type: mongoose.Schema.Types.ObjectId,
    ref:'Driver',
  },
  isActive: {
    type: Boolean,
    default: false, // Defaults to false if not specified
  },
  adminCreatedBy: { type: String },
  adminCompanyName: { type: String },
  adminCompanyId: { type: String },
});

// Create the model
export default mongoose.models.DriverDocuments || mongoose.model("DriverDocuments", DriverDocumentsSchema);
