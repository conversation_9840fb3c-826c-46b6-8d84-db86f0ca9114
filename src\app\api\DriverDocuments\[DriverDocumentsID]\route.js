import { connect } from "@config/db.js";
import DriverDocument from "@models/DriverDocuments/DriverDocuments.Model.js";
import { NextResponse } from "next/server";

/* ========================= UPDATE DOCUMENT ========================= */
export const PUT = async (request, context) => {
  try {
    await connect();
    const id = context.params.DriverDocumentsID;
    const data = await request.json();

    const {
      document,
      documentname,
      driverid,
      isActive,
      documentExpire,
      adminCreatedBy,
    } = data;

    const doc = await DriverDocument.findById(id);
    if (!doc) {
      return NextResponse.json({ error: "Document not found", status: 404 });
    }

    const update = {};
    if (document) {
      update.document = document ? document.trim() : DriverDocument.document;
    }
    if (documentname) {
      update.documentname = documentname ? documentname.trim() : DriverDocument.documentname;
    }
    if(driverid){
      update.driverid = driverid ? driverid : DriverDocument.driverid;
    }
    
    if (isActive !== undefined) {
      update.isActive = isActive ? isActive : DriverDocument.isActive;
    }

    if(adminCreatedBy){
      update.adminCreatedBy = adminCreatedBy ? adminCreatedBy : DriverDocument.adminCreatedBy;
    }
    if(documentExpire){
      update.documentExpire = documentExpire ? documentExpire : DriverDocument.documentExpire;
    }

    const updated = await DriverDocument.findByIdAndUpdate(id, update, { new: true });

    return NextResponse.json({
      message: "Document updated successfully",
      data: updated,
      status: 200,
    });

  } catch (error) {
    console.error("PUT Error:", error);
    return NextResponse.json({
      error: "Failed to update Document",
      status: 500,
    });
  }
};

/* ========================= GET DOCUMENT BY ID ========================= */
export const GET = async (request, context) => {
  try {
    await connect();
    const id = context.params.DriverDocumentsID;
    const doc = await DriverDocument.findById(id);

    if (!doc) {
      return NextResponse.json({ error: "Document not found", status: 404 });
    }

    return NextResponse.json({ data: doc, status: 200 });
  } catch (error) {
    console.error("GET Error:", error);
    return NextResponse.json({ error: "Failed to fetch Document", status: 500 });
  }
};

/* ========================= DELETE DOCUMENT ========================= */
export const DELETE = async (request, context) => {
  try {
    await connect();
    const id = context.params.DriverDocumentsID;

    const deleted = await DriverDocument.findByIdAndDelete(id);
    if (!deleted) {
      return NextResponse.json({ error: "Document not found", status: 404 });
    }

    return NextResponse.json({
      message: "Document deleted successfully",
      success: true,
      status: 200,
    });
  } catch (error) {
    console.error("DELETE Error:", error);
    return NextResponse.json({
      error: "An error occurred while deleting the Document",
      status: 500,
    });
  }
};
